package com.shinet.core.safe.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shinet.core.safe.core.util.JsonUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备信息扩展表
 * 用于存储设备详细信息，支持规则匹配和CAID获取操作
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Slf4j
public class DeviceInfoExtension implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备名称/设备唯一标识")
    private String deviceName;

    @ApiModelProperty(value = "产品标识")
    private String product;

    @ApiModelProperty(value = "操作系统：android/ios")
    private String os;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    // ========== 应用状态字段 ==========
    @JsonProperty("VPN")
    @JSONField(name = "VPN")
    @ApiModelProperty(value = "是否启用VPN")
    private Boolean vpnEnabled;

    @JsonProperty("weixin")
    @JSONField(name = "weixin")
    @ApiModelProperty(value = "是否安装微信")
    private Boolean weixinInstalled;

    @JsonProperty("zhifubao")
    @JSONField(name = "zhifubao")
    @ApiModelProperty(value = "是否安装支付宝")
    private Boolean zhifubaoInstalled;

    @JsonProperty("douyin")
    @JSONField(name = "douyin")
    @ApiModelProperty(value = "是否安装抖音")
    private Boolean douyinInstalled;

    @JsonProperty("isCaptured")
    @JSONField(name = "isCaptured")
    @ApiModelProperty(value = "是否被捕获")
    private Boolean captured;

    @JsonProperty("broken")
    @JSONField(name = "broken")
    @ApiModelProperty(value = "是否损坏")
    private Boolean broken;

    // ========== 系统信息字段 ==========
    @JsonProperty("systemVersion")
    @JSONField(name = "systemVersion")
    @ApiModelProperty(value = "系统版本")
    private String systemVersion;

    @JsonProperty("language")
    @JSONField(name = "language")
    @ApiModelProperty(value = "语言设置")
    private String language;

    @JsonProperty("countryCode")
    @JSONField(name = "countryCode")
    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @JsonProperty("timeZone")
    @JSONField(name = "timeZone")
    @ApiModelProperty(value = "时区")
    private String timeZone;

    // ========== 硬件信息字段 ==========
    @JsonProperty("machine")
    @JSONField(name = "machine")
    @ApiModelProperty(value = "设备型号")
    private String machine;

    @JsonProperty("modelF")
    @JSONField(name = "modelF")
    @ApiModelProperty(value = "模型标识")
    private String modelF;

    @JsonProperty("disk")
    @JSONField(name = "disk")
    @ApiModelProperty(value = "磁盘大小(字节)")
    private String diskSize;

    @JsonProperty("memory")
    @JSONField(name = "memory")
    @ApiModelProperty(value = "内存大小(字节)")
    private String memorySize;

    // ========== 网络信息字段 ==========
    @JsonProperty("carrierInfo")
    @JSONField(name = "carrierInfo")
    @ApiModelProperty(value = "运营商信息")
    private String carrierInfo;

    // ========== 时间信息字段 ==========
    @JsonProperty("sysFileTime")
    @JSONField(name = "sysFileTime")
    @ApiModelProperty(value = "系统文件时间")
    private String sysFileTime;

    @JsonProperty("bootTimeInSec")
    @JSONField(name = "bootTimeInSec")
    @ApiModelProperty(value = "启动时间(秒)")
    private String bootTimeInSec;

    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    @ApiModelProperty(value = "时间戳")
    private String timestamp;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // ========== 操作系统常量 ==========
    public static final String OS_ANDROID = "android";
    public static final String OS_IOS = "ios";

    /**
     * 从JSON字符串创建对象
     * 
     * @param jsonString JSON字符串
     * @return DeviceInfoExtension对象，解析失败返回null
     */
    public static DeviceInfoExtension fromJson(String jsonString) {
        try {
            return JsonUtils.parseObject(jsonString, DeviceInfoExtension.class);
        } catch (Exception e) {
            log.error("DeviceInfoExtension从JSON创建对象失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 转换为JSON字符串
     * 
     * @return JSON字符串，转换失败返回null
     */
    public String toJson() {
        try {
            return JsonUtils.toJsonString(this);
        } catch (Exception e) {
            log.error("DeviceInfoExtension转换为JSON失败: deviceName={}", this.deviceName, e);
            return null;
        }
    }

    /**
     * 数据校验
     * 检查必要字段是否完整
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        // 检查必要字段
        if (deviceName == null || deviceName.trim().isEmpty()) {
            return false;
        }
        if (product == null || product.trim().isEmpty()) {
            return false;
        }
        if (os == null || os.trim().isEmpty()) {
            return false;
        }
        
        // 检查操作系统是否有效
        if (!OS_ANDROID.equalsIgnoreCase(os) && !OS_IOS.equalsIgnoreCase(os)) {
            return false;
        }
        
        return true;
    }



    /**
     * 从原始JSON数据创建并设置基础信息
     * 
     * @param jsonString 原始JSON字符串
     * @param product 产品标识
     * @param os 操作系统
     * @return DeviceInfoExtension对象
     */
    public static DeviceInfoExtension fromJsonWithBasicInfo(String jsonString, String product, String os) {
        DeviceInfoExtension extension = fromJson(jsonString);
        if (extension != null) {
            extension.setProduct(product);
            extension.setOs(os);
        }
        return extension;
    }
}
