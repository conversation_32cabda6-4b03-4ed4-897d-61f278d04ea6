package com.shinet.core.safe.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shinet.core.safe.core.util.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备信息扩展对象
 * 用于映射设备详细信息，支持规则匹配和CAID获取操作
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Slf4j
public class DeviceInfoExtension implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 设备名称/设备唯一标识 */
    private String deviceName;

    /** 产品标识 */
    private String product;

    /** 操作系统：android/ios */
    private String os;

    /** 用户ID */
    private String userId;

    // ========== 应用状态字段 ==========
    /** VPN状态 */
    @JsonProperty("VPN")
    @JSONField(name = "VPN")
    private Boolean VPN;

    /** 微信状态 */
    @JsonProperty("weixin")
    @JSONField(name = "weixin")
    private Boolean weixin;

    /** 支付宝状态 */
    @JsonProperty("zhifubao")
    @JSONField(name = "zhifubao")
    private Boolean zhifubao;

    /** 抖音状态 */
    @JsonProperty("douyin")
    @JSONField(name = "douyin")
    private Boolean douyin;

    /** 是否被捕获 */
    @JsonProperty("isCaptured")
    @JSONField(name = "isCaptured")
    private Boolean isCaptured;

    /** 是否损坏 */
    @JsonProperty("broken")
    @JSONField(name = "broken")
    private Boolean broken;

    // ========== 系统信息字段 ==========
    /** 系统版本 */
    @JsonProperty("systemVersion")
    @JSONField(name = "systemVersion")
    private String systemVersion;

    /** 语言设置 */
    @JsonProperty("language")
    @JSONField(name = "language")
    private String language;

    /** 国家代码 */
    @JsonProperty("countryCode")
    @JSONField(name = "countryCode")
    private String countryCode;

    /** 时区 */
    @JsonProperty("timeZone")
    @JSONField(name = "timeZone")
    private String timeZone;

    // ========== 硬件信息字段 ==========
    /** 设备型号 */
    @JsonProperty("machine")
    @JSONField(name = "machine")
    private String machine;

    /** 模型标识 */
    @JsonProperty("modelF")
    @JSONField(name = "modelF")
    private String modelF;

    /** 磁盘大小(字节) */
    @JsonProperty("disk")
    @JSONField(name = "disk")
    private String disk;

    /** 内存大小(字节) */
    @JsonProperty("memory")
    @JSONField(name = "memory")
    private String memory;

    // ========== 网络信息字段 ==========
    /** 运营商信息 */
    @JsonProperty("carrierInfo")
    @JSONField(name = "carrierInfo")
    private String carrierInfo;

    // ========== 时间信息字段 ==========
    /** 系统文件时间 */
    @JsonProperty("sysFileTime")
    @JSONField(name = "sysFileTime")
    private String sysFileTime;

    /** 启动时间(秒) */
    @JsonProperty("bootTimeInSec")
    @JSONField(name = "bootTimeInSec")
    private String bootTimeInSec;

    /** 时间戳 */
    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    private String timestamp;

    /**
     * 从JSON字符串创建对象
     * 
     * @param jsonString JSON字符串
     * @return DeviceInfoExtension对象，解析失败返回null
     */
    public static DeviceInfoExtension fromJson(String jsonString) {
        try {
            return JsonUtils.parseObject(jsonString, DeviceInfoExtension.class);
        } catch (Exception e) {
            log.error("DeviceInfoExtension从JSON创建对象失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 转换为JSON字符串
     * 
     * @return JSON字符串，转换失败返回null
     */
    public String toJson() {
        try {
            return JsonUtils.toJsonString(this);
        } catch (Exception e) {
            log.error("DeviceInfoExtension转换为JSON失败: deviceName={}", this.deviceName, e);
            return null;
        }
    }

    /**
     * 数据校验
     * 检查必要字段是否完整
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        // 检查必要字段
        if (deviceName == null || deviceName.trim().isEmpty()) {
            return false;
        }
        if (product == null || product.trim().isEmpty()) {
            return false;
        }
        if (os == null || os.trim().isEmpty()) {
            return false;
        }

        
        return true;
    }

    /**
     * 设置基础信息的便捷方法
     *
     * @param deviceName 设备名称
     * @param product 产品标识
     * @param os 操作系统
     * @param userId 用户ID
     * @return 当前对象
     */
    public DeviceInfoExtension setBasicInfo(String deviceName, String product, String os, String userId) {
        this.deviceName = deviceName;
        this.product = product;
        this.os = os;
        this.userId = userId;
        return this;
    }

    /**
     * 创建设备信息扩展对象的静态工厂方法
     *
     * @param deviceName 设备名称
     * @param product 产品标识
     * @param os 操作系统
     * @return DeviceInfoExtension对象
     */
    public static DeviceInfoExtension create(String deviceName, String product, String os) {
        return new DeviceInfoExtension()
                .setBasicInfo(deviceName, product, os, null);
    }

    /**
     * 从原始JSON数据创建并设置基础信息
     *
     * @param jsonString 原始JSON字符串
     * @param product 产品标识
     * @param os 操作系统
     * @return DeviceInfoExtension对象
     */
    public static DeviceInfoExtension fromJsonWithBasicInfo(String jsonString, String product, String os) {
        DeviceInfoExtension extension = fromJson(jsonString);
        if (extension != null) {
            extension.setProduct(product);
            extension.setOs(os);
            // 如果JSON中没有deviceName，使用默认逻辑
            if (extension.getDeviceName() == null) {
                extension.setDeviceName(generateDeviceNameFromJson(jsonString));
            }
        }
        return extension;
    }

    /**
     * 从JSON中生成设备名称的辅助方法
     *
     * @param jsonString JSON字符串
     * @return 生成的设备名称
     */
    private static String generateDeviceNameFromJson(String jsonString) {
        // 可以根据业务需求实现设备名称生成逻辑
        // 这里先返回一个默认值
        return "unknown_device_" + System.currentTimeMillis();
    }
}
